<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player - NCast Podcast App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            width: 428px;
            height: 926px;
            position: relative;
            overflow: hidden;
        }

        /* 验证机制样式 */
        .validation-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            font-size: 12px;
            z-index: 9999;
            display: none;
            padding: 10px;
            overflow-y: auto;
        }

        /* Player容器 */
        .Player {
            position: absolute;
            left: 0;
            top: 0;
            width: 428px;
            height: 926px;
            background: #ffffff;
        }

        /* 返回按钮 */
        .arrow-circle-left-fill {
            position: absolute;
            left: 32px;
            top: 58px;
            width: 48px;
            height: 48px;
            opacity: 0.1;
        }

        .arrow-circle-left-fill img {
            display: block;
            width: 100%;
            height: 100%;
        }

        /* 标题 */
        .now-playing {
            position: absolute;
            left: 104px;
            top: 68px;
            width: 142px;
            height: 28px;
            font-size: 20px;
            font-weight: 600;
            color: #1f1f1f;
            line-height: 28px;
        }

        /* 更多按钮 */
        .dots-three-outline-vertical-fill {
            position: absolute;
            left: 372px;
            top: 70px;
            width: 24px;
            height: 24px;
            opacity: 0.5;
        }

        .dots-three-outline-vertical-fill img {
            display: block;
            width: 100%;
            height: 100%;
        }

        /* 播客封面 */
        .mask-group {
            position: absolute;
            left: 32px;
            top: 138px;
            width: 364px;
            height: 364px;
            border-radius: 24px;
            overflow: hidden;
        }

        .mask-group img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 操作按钮组 */
        .group-175 {
            position: absolute;
            left: 81px;
            top: 466px;
            width: 266px;
            height: 72px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .share-network-fill, .heart-fill, .archive-box-fill {
            width: 32px;
            height: 32px;
            opacity: 0.5;
        }

        .share-network-fill img, .heart-fill img, .archive-box-fill img {
            display: block;
            width: 100%;
            height: 100%;
        }

        /* 播客标题 */
        .sunday-vibes-rift {
            position: absolute;
            left: 107px;
            top: 570px;
            width: 215px;
            height: 28px;
            font-size: 20px;
            font-weight: 600;
            color: #1f1f1f;
            line-height: 28px;
        }

        /* 分类标签 */
        .entertainment {
            position: absolute;
            left: 161px;
            top: 608px;
            width: 106px;
            height: 19px;
            font-size: 14px;
            color: #1f1f1f;
            opacity: 0.7;
            line-height: 19px;
        }

        /* 时间显示 */
        .time-07-00 {
            position: absolute;
            left: 32px;
            top: 667px;
            width: 43px;
            height: 19px;
            font-size: 14px;
            color: #1f1f1f;
            opacity: 0.7;
            line-height: 19px;
        }

        .time-15-00 {
            position: absolute;
            left: 355px;
            top: 667px;
            width: 41px;
            height: 19px;
            font-size: 14px;
            color: #1f1f1f;
            opacity: 0.7;
            line-height: 19px;
        }

        /* 进度条 */
        .group-6 {
            position: absolute;
            left: 32px;
            top: 710px;
            width: 364px;
            height: 60px;
        }

        .group-6 img {
            display: block;
            width: 100%;
            height: 100%;
        }

        /* 播放按钮 */
        .group-162 {
            position: absolute;
            left: 174px;
            top: 810px;
            width: 80px;
            height: 80px;
        }

        .group-162 img {
            display: block;
            width: 100%;
            height: 100%;
        }

        /* 控制按钮 */
        .clock-counter-clockwise-fill {
            position: absolute;
            left: 110px;
            top: 834px;
            width: 32px;
            height: 32px;
            opacity: 0.7;
        }

        .clock-counter-clockwise-fill img {
            display: block;
            width: 100%;
            height: 100%;
        }

        .clock-clockwise-fill {
            position: absolute;
            left: 286px;
            top: 834px;
            width: 32px;
            height: 32px;
            opacity: 0.7;
        }

        .clock-clockwise-fill img {
            display: block;
            width: 100%;
            height: 100%;
        }

        .arrows-down-up-fill {
            position: absolute;
            left: 46px;
            top: 834px;
            width: 32px;
            height: 32px;
            opacity: 0.7;
        }

        .arrows-down-up-fill img {
            display: block;
            width: 100%;
            height: 100%;
        }

        .sort-ascending-fill {
            position: absolute;
            left: 350px;
            top: 834px;
            width: 32px;
            height: 32px;
            opacity: 0.7;
        }

        .sort-ascending-fill img {
            display: block;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="validation-overlay" id="validationOverlay"></div>
    
    <div class="Player">
        <!-- 返回按钮 -->
        <div class="arrow-circle-left-fill">
            <img src="arrow-circle-left-fill.png" alt="返回">
        </div>

        <!-- 标题 -->
        <div class="now-playing">Now Playing</div>

        <!-- 更多按钮 -->
        <div class="dots-three-outline-vertical-fill">
            <img src="dots-three-outline-vertical-fill.png" alt="更多">
        </div>

        <!-- 播客封面 -->
        <div class="mask-group">
            <img src="podcast-cover.png" alt="播客封面">
        </div>

        <!-- 操作按钮组 -->
        <div class="group-175">
            <div class="share-network-fill">
                <img src="share-network-fill.png" alt="分享">
            </div>
            <div class="heart-fill">
                <img src="heart-fill.png" alt="喜欢">
            </div>
            <div class="archive-box-fill">
                <img src="archive-box-fill.png" alt="收藏">
            </div>
        </div>

        <!-- 播客标题 -->
        <div class="sunday-vibes-rift">Sunday Vibes - Rift</div>

        <!-- 分类标签 -->
        <div class="entertainment">Entertainment</div>

        <!-- 时间显示 -->
        <div class="time-07-00">07:00</div>
        <div class="time-15-00">15:00</div>

        <!-- 进度条 -->
        <div class="group-6">
            <img src="group-6.png" alt="进度条">
        </div>

        <!-- 播放按钮 -->
        <div class="group-162">
            <img src="group-162.png" alt="播放">
        </div>

        <!-- 控制按钮 -->
        <div class="arrows-down-up-fill">
            <img src="arrows-down-up-fill.png" alt="随机播放">
        </div>

        <div class="clock-counter-clockwise-fill">
            <img src="clock-counter-clockwise-fill.png" alt="倒退">
        </div>

        <div class="clock-clockwise-fill">
            <img src="clock-clockwise-fill.png" alt="快进">
        </div>

        <div class="sort-ascending-fill">
            <img src="sort-ascending-fill.png" alt="排序">
        </div>
    </div>

    <script>
        // 验证机制
        function validateLayout() {
            const results = [];
            const elements = document.querySelectorAll('[class]');
            
            elements.forEach(element => {
                const className = element.className;
                const rect = element.getBoundingClientRect();
                const styles = window.getComputedStyle(element);
                
                results.push({
                    class: className,
                    position: {
                        x: Math.round(rect.left),
                        y: Math.round(rect.top),
                        width: Math.round(rect.width),
                        height: Math.round(rect.height)
                    },
                    styles: {
                        fontSize: styles.fontSize,
                        color: styles.color,
                        opacity: styles.opacity,
                        zIndex: styles.zIndex
                    }
                });
            });
            
            return results;
        }

        // 显示验证结果
        function showValidation() {
            const overlay = document.getElementById('validationOverlay');
            const results = validateLayout();
            
            let html = '<h3>布局验证结果:</h3>';
            results.forEach(result => {
                html += `
                    <div style="margin: 10px 0; padding: 5px; border: 1px solid #333;">
                        <strong>${result.class}</strong><br>
                        位置: x=${result.position.x}, y=${result.position.y}<br>
                        尺寸: ${result.position.width}x${result.position.height}<br>
                        字体: ${result.styles.fontSize}, 颜色: ${result.styles.color}<br>
                        透明度: ${result.styles.opacity}, 层级: ${result.styles.zIndex}
                    </div>
                `;
            });
            
            overlay.innerHTML = html + '<button onclick="hideValidation()">关闭</button>';
            overlay.style.display = 'block';
        }

        function hideValidation() {
            document.getElementById('validationOverlay').style.display = 'none';
        }

        // 双击显示验证信息
        document.addEventListener('dblclick', showValidation);
    </script>
</body>
</html>
